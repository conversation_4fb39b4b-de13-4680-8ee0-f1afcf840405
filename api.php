<?php
// api.php - Backend pro ukládání dat SK Blučina

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Pokud je to OPTIONS request (preflight), jen od<PERSON><PERSON><PERSON>
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Cesta k souboru s daty
$dataFile = 'data/team_data.json';

// Ujistit se, že složka data existuje
if (!file_exists('data')) {
    mkdir('data', 0755, true);
}

// Výchozí data
$defaultData = [
    'players' => [
        [
            'id' => 1,
            'firstName' => 'Jan',
            'lastName' => 'Novák',
            'positions' => ['Brankář'],
            'capacity' => 100,
            'status' => 'hraje',
            'number' => 1
        ],
        [
            'id' => 2,
            'firstName' => 'Petr',
            'lastName' => 'Svoboda',
            'positions' => ['Obránce'],
            'capacity' => 85,
            'status' => 'hraje',
            'number' => 5
        ],
        [
            'id' => 3,
            'firstName' => 'Tomáš',
            'lastName' => 'Dvořák',
            'positions' => ['Záložník', 'Útočník'],
            'capacity' => 75,
            'status' => 'zjišťujeme',
            'number' => 10
        ]
    ],
    'staff' => [
        [
            'id' => 1,
            'firstName' => 'Milan',
            'lastName' => 'Kouč',
            'role' => 'Hlavní trenér'
        ],
        [
            'id' => 2,
            'firstName' => 'Pavel',
            'lastName' => 'Asistent',
            'role' => 'Asistent trenéra'
        ]
    ],
    'injured' => [
        [
            'id' => 1,
            'firstName' => 'Martin',
            'lastName' => 'Zraněný',
            'positions' => ['Obránce'],
            'expectedReturn' => '2025-07-01'
        ]
    ],
    'formation' => [],
    'lastUpdated' => date('Y-m-d H:i:s')
];

// Funkce pro načtení dat
function loadData($file, $default) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        if ($content !== false) {
            $data = json_decode($content, true);
            if ($data !== null) {
                return $data;
            }
        }
    }
    return $default;
}

// Funkce pro uložení dat
function saveData($file, $data) {
    $data['lastUpdated'] = date('Y-m-d H:i:s');
    $jsonData = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    return file_put_contents($file, $jsonData, LOCK_EX) !== false;
}

// Zpracování požadavků
$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    switch ($method) {
        case 'GET':
            // Načíst všechna data
            $data = loadData($dataFile, $defaultData);
            echo json_encode(['success' => true, 'data' => $data]);
            break;

        case 'POST':
            // Uložit všechna data
            if (!$input) {
                throw new Exception('Neplatná data');
            }
            
            $currentData = loadData($dataFile, $defaultData);
            
            // Aktualizovat pouze poskytnuté sekce
            if (isset($input['players'])) {
                $currentData['players'] = $input['players'];
            }
            if (isset($input['staff'])) {
                $currentData['staff'] = $input['staff'];
            }
            if (isset($input['injured'])) {
                $currentData['injured'] = $input['injured'];
            }
            if (isset($input['formation'])) {
                $currentData['formation'] = $input['formation'];
            }
            
            if (saveData($dataFile, $currentData)) {
                echo json_encode(['success' => true, 'message' => 'Data uložena']);
            } else {
                throw new Exception('Chyba při ukládání dat');
            }
            break;

        case 'PUT':
            // Aktualizovat konkrétní sekci
            if (!$input || !isset($input['section']) || !isset($input['data'])) {
                throw new Exception('Neplatný formát dat');
            }
            
            $currentData = loadData($dataFile, $defaultData);
            $section = $input['section'];
            
            if (!in_array($section, ['players', 'staff', 'injured', 'formation'])) {
                throw new Exception('Neplatná sekce');
            }
            
            $currentData[$section] = $input['data'];
            
            if (saveData($dataFile, $currentData)) {
                echo json_encode(['success' => true, 'message' => "Sekce $section aktualizována"]);
            } else {
                throw new Exception('Chyba při ukládání dat');
            }
            break;

        case 'DELETE':
            // Reset dat na výchozí hodnoty
            if (saveData($dataFile, $defaultData)) {
                echo json_encode(['success' => true, 'message' => 'Data resetována']);
            } else {
                throw new Exception('Chyba při resetování dat');
            }
            break;

        default:
            throw new Exception('Neplatná HTTP metoda');
    }

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => $e->getMessage()]);
}
?>