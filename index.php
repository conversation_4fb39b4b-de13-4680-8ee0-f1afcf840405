<?php
// Pokud je to API požadavek, zpracovat ho
if (isset($_GET['api']) || strpos($_SERVER['REQUEST_URI'], '/api') !== false) {
    header('Content-Type: application/json');
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type');

    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        http_response_code(200);
        exit();
    }

    $dataFile = 'data/team_data.json';

    if (!file_exists('data')) {
        mkdir('data', 0755, true);
    }

    $defaultData = [
        'players' => [
            [
                'id' => 1,
                'firstName' => 'Jan',
                'lastName' => 'Novák',
                'positions' => ['Brankář'],
                'capacity' => 100,
                'status' => 'hraje',
                'number' => 1
            ],
            [
                'id' => 2,
                'firstName' => 'Petr',
                'lastName' => 'Svoboda',
                'positions' => ['Obránce'],
                'capacity' => 85,
                'status' => 'hraje',
                'number' => 5
            ],
            [
                'id' => 3,
                'firstName' => 'Tomáš',
                'lastName' => 'Dvořák',
                'positions' => ['Záložník', 'Útočník'],
                'capacity' => 75,
                'status' => 'zjišťujeme',
                'number' => 10
            ]
        ],
        'staff' => [
            [
                'id' => 1,
                'firstName' => 'Milan',
                'lastName' => 'Kouč',
                'role' => 'Hlavní trenér'
            ],
            [
                'id' => 2,
                'firstName' => 'Pavel',
                'lastName' => 'Asistent',
                'role' => 'Asistent trenéra'
            ]
        ],
        'injured' => [
            [
                'id' => 1,
                'firstName' => 'Martin',
                'lastName' => 'Zraněný',
                'positions' => ['Obránce'],
                'expectedReturn' => '2025-07-01'
            ]
        ],
        'formation' => [],
        'lastUpdated' => date('Y-m-d H:i:s')
    ];

    function loadData($file, $default) {
        if (file_exists($file)) {
            $content = file_get_contents($file);
            if ($content !== false) {
                $data = json_decode($content, true);
                if ($data !== null) {
                    return $data;
                }
            }
        }
        return $default;
    }

    function saveData($file, $data) {
        $data['lastUpdated'] = date('Y-m-d H:i:s');
        $jsonData = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        return file_put_contents($file, $jsonData, LOCK_EX) !== false;
    }

    $method = $_SERVER['REQUEST_METHOD'];
    $input = json_decode(file_get_contents('php://input'), true);

    try {
        switch ($method) {
            case 'GET':
                $data = loadData($dataFile, $defaultData);
                echo json_encode(['success' => true, 'data' => $data]);
                break;

            case 'POST':
                if (!$input) {
                    throw new Exception('Neplatná data');
                }
                
                $currentData = loadData($dataFile, $defaultData);
                
                if (isset($input['players'])) {
                    $currentData['players'] = $input['players'];
                }
                if (isset($input['staff'])) {
                    $currentData['staff'] = $input['staff'];
                }
                if (isset($input['injured'])) {
                    $currentData['injured'] = $input['injured'];
                }
                if (isset($input['formation'])) {
                    $currentData['formation'] = $input['formation'];
                }
                
                if (saveData($dataFile, $currentData)) {
                    echo json_encode(['success' => true, 'message' => 'Data uložena']);
                } else {
                    throw new Exception('Chyba při ukládání dat');
                }
                break;

            case 'PUT':
                if (!$input || !isset($input['section']) || !isset($input['data'])) {
                    throw new Exception('Neplatný formát dat');
                }
                
                $currentData = loadData($dataFile, $defaultData);
                $section = $input['section'];
                
                if (!in_array($section, ['players', 'staff', 'injured', 'formation'])) {
                    throw new Exception('Neplatná sekce');
                }
                
                $currentData[$section] = $input['data'];
                
                if (saveData($dataFile, $currentData)) {
                    echo json_encode(['success' => true, 'message' => "Sekce $section aktualizována"]);
                } else {
                    throw new Exception('Chyba při ukládání dat');
                }
                break;

            default:
                throw new Exception('Neplatná HTTP metoda');
        }

    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
    exit();
}
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SK Blučina - Správa týmu</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect, useRef } = React;
        const { Plus, Edit3, Trash2, Users, Heart, Shield, Save, RefreshCw } = lucide;

        // API funkce - používá stejný soubor s ?api parametrem
        const API = {
            baseUrl: window.location.pathname + '?api=1',

            async loadData() {
                try {
                    const response = await fetch(this.baseUrl);
                    const result = await response.json();
                    if (result.success) {
                        return result.data;
                    } else {
                        throw new Error(result.error || 'Chyba při načítání dat');
                    }
                } catch (error) {
                    console.error('Chyba při načítání dat:', error);
                    throw error;
                }
            },

            async saveData(data) {
                try {
                    const response = await fetch(this.baseUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(data)
                    });
                    const result = await response.json();
                    if (!result.success) {
                        throw new Error(result.error || 'Chyba při ukládání dat');
                    }
                    return result;
                } catch (error) {
                    console.error('Chyba při ukládání dat:', error);
                    throw error;
                }
            },

            async saveSection(section, data) {
                try {
                    const response = await fetch(this.baseUrl, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ section, data })
                    });
                    const result = await response.json();
                    if (!result.success) {
                        throw new Error(result.error || 'Chyba při ukládání sekce');
                    }
                    return result;
                } catch (error) {
                    console.error('Chyba při ukládání sekce:', error);
                    throw error;
                }
            }
        };

        const FootballTeamManager = () => {
            // Výchozí data
            const defaultData = {
                players: [
                    { id: 1, firstName: 'Jan', lastName: 'Novák', positions: ['Brankář'], capacity: 100, status: 'hraje', number: 1 },
                    { id: 2, firstName: 'Petr', lastName: 'Svoboda', positions: ['Obránce'], capacity: 85, status: 'hraje', number: 5 },
                    { id: 3, firstName: 'Tomáš', lastName: 'Dvořák', positions: ['Záložník', 'Útočník'], capacity: 75, status: 'zjišťujeme', number: 10 }
                ],
                staff: [
                    { id: 1, firstName: 'Milan', lastName: 'Kouč', role: 'Hlavní trenér' },
                    { id: 2, firstName: 'Pavel', lastName: 'Asistent', role: 'Asistent trenéra' }
                ],
                injured: [
                    { id: 1, firstName: 'Martin', lastName: 'Zraněný', positions: ['Obránce'], expectedReturn: '2025-07-01' }
                ],
                formation: []
            };

            // Stavy
            const [players, setPlayers] = useState(defaultData.players);
            const [staff, setStaff] = useState(defaultData.staff);
            const [injured, setInjured] = useState(defaultData.injured);
            const [formation, setFormation] = useState(defaultData.formation);
            const [activeTab, setActiveTab] = useState('players');
            const [showForm, setShowForm] = useState(false);
            const [editingPlayer, setEditingPlayer] = useState(null);
            const [draggedPlayer, setDraggedPlayer] = useState(null);
            const [isLoading, setIsLoading] = useState(true);
            const [isSaving, setIsSaving] = useState(false);
            const [lastSaved, setLastSaved] = useState(null);
            const [saveError, setSaveError] = useState(null);

            // Formulářové stavy
            const [formData, setFormData] = useState({
                firstName: '', lastName: '', positions: [], capacity: 100, status: 'hraje', number: ''
            });

            // Načtení dat při startu
            useEffect(() => {
                loadInitialData();
            }, []);

            const loadInitialData = async () => {
                try {
                    setIsLoading(true);
                    const data = await API.loadData();
                    setPlayers(data.players || defaultData.players);
                    setStaff(data.staff || defaultData.staff);
                    setInjured(data.injured || defaultData.injured);
                    setFormation(data.formation || defaultData.formation);
                    setLastSaved(data.lastUpdated ? new Date(data.lastUpdated) : null);
                } catch (error) {
                    console.error('Chyba při načítání:', error);
                    // Použít výchozí data při chybě
                    setPlayers(defaultData.players);
                    setStaff(defaultData.staff);
                    setInjured(defaultData.injured);
                    setFormation(defaultData.formation);
                } finally {
                    setIsLoading(false);
                }
            };

            // Automatické ukládání při změnách
            const saveToServer = async (section, data) => {
                try {
                    setIsSaving(true);
                    setSaveError(null);
                    await API.saveSection(section, data);
                    setLastSaved(new Date());
                } catch (error) {
                    setSaveError(error.message);
                    console.error('Chyba při ukládání:', error);
                } finally {
                    setIsSaving(false);
                }
            };

            // Debounced save function
            const debouncedSave = useRef();
            useEffect(() => {
                if (debouncedSave.current) {
                    clearTimeout(debouncedSave.current);
                }
                debouncedSave.current = setTimeout(() => {
                    if (!isLoading) {
                        saveToServer('players', players);
                    }
                }, 1000);
            }, [players, isLoading]);

            useEffect(() => {
                if (debouncedSave.current) {
                    clearTimeout(debouncedSave.current);
                }
                debouncedSave.current = setTimeout(() => {
                    if (!isLoading) {
                        saveToServer('formation', formation);
                    }
                }, 1000);
            }, [formation, isLoading]);

            // Pozice na hřišti
            const fieldPositions = [
                { id: 'gk', x: 50, y: 85, label: 'GK' },
                { id: 'lb', x: 15, y: 65, label: 'LB' },
                { id: 'cb1', x: 35, y: 65, label: 'CB' },
                { id: 'cb2', x: 65, y: 65, label: 'CB' },
                { id: 'rb', x: 85, y: 65, label: 'RB' },
                { id: 'lm', x: 15, y: 35, label: 'LM' },
                { id: 'cm1', x: 35, y: 35, label: 'CM' },
                { id: 'cm2', x: 65, y: 35, label: 'CM' },
                { id: 'rm', x: 85, y: 35, label: 'RM' },
                { id: 'st1', x: 35, y: 15, label: 'ST' },
                { id: 'st2', x: 65, y: 15, label: 'ST' }
            ];

            const handleSubmit = () => {
                if (!formData.firstName.trim() || !formData.lastName.trim()) {
                    alert('Vyplňte prosím jméno a příjmení');
                    return;
                }
                
                if (editingPlayer) {
                    setPlayers(players.map(p => p.id === editingPlayer.id ? 
                        { ...editingPlayer, ...formData, id: editingPlayer.id } : p
                    ));
                    setEditingPlayer(null);
                } else {
                    const newPlayer = {
                        id: Date.now(),
                        ...formData,
                        positions: formData.positions.filter(p => p.trim())
                    };
                    setPlayers([...players, newPlayer]);
                }
                setFormData({ firstName: '', lastName: '', positions: [], capacity: 100, status: 'hraje', number: '' });
                setShowForm(false);
            };

            const handleEdit = (player) => {
                setEditingPlayer(player);
                setFormData({
                    firstName: player.firstName,
                    lastName: player.lastName,
                    positions: player.positions,
                    capacity: player.capacity,
                    status: player.status,
                    number: player.number || ''
                });
                setShowForm(true);
            };

            const handleDelete = (id) => {
                if (confirm('Opravdu chcete odstranit tohoto hráče?')) {
                    setPlayers(players.filter(p => p.id !== id));
                }
            };

            const handleDragStart = (e, player) => {
                setDraggedPlayer(player);
                e.dataTransfer.effectAllowed = 'move';
            };

            const handleDragOver = (e) => {
                e.preventDefault();
            };

            const handleDrop = (e, position) => {
                e.preventDefault();
                if (draggedPlayer) {
                    const newFormation = formation.filter(p => p.position !== position.id);
                    newFormation.push({ ...draggedPlayer, position: position.id });
                    setFormation(newFormation);
                    setDraggedPlayer(null);
                }
            };

            const getPlayerAtPosition = (positionId) => {
                return formation.find(p => p.position === positionId);
            };

            const getStatusColor = (status) => {
                switch (status) {
                    case 'hraje': return 'bg-green-100 text-green-800';
                    case 'zjišťujeme': return 'bg-yellow-100 text-yellow-800';
                    case 'odmítl': return 'bg-red-100 text-red-800';
                    default: return 'bg-gray-100 text-gray-800';
                }
            };

            const getCapacityColor = (capacity) => {
                if (capacity >= 90) return 'text-green-600';
                if (capacity >= 70) return 'text-yellow-600';
                return 'text-red-600';
            };

            const manualSave = async () => {
                try {
                    setIsSaving(true);
                    setSaveError(null);
                    await API.saveData({ players, staff, injured, formation });
                    setLastSaved(new Date());
                    alert('Data úspěšně uložena!');
                } catch (error) {
                    setSaveError(error.message);
                    alert('Chyba při ukládání: ' + error.message);
                } finally {
                    setIsSaving(false);
                }
            };

            if (isLoading) {
                return React.createElement('div', { className: "min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center" },
                    React.createElement('div', { className: "text-center" },
                        React.createElement('div', { className: "animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4" }),
                        React.createElement('p', { className: "text-gray-600" }, 'Načítám data...')
                    )
                );
            }

            // Zbytek komponenty stejně jako v HTML verzi...
            return React.createElement('div', { className: "min-h-screen bg-gradient-to-br from-green-50 to-blue-50 p-4" },
                React.createElement('div', { className: "max-w-7xl mx-auto" },
                    React.createElement('div', { className: "bg-white rounded-lg shadow-lg p-6 mb-6" },
                        React.createElement('div', { className: "flex justify-between items-center" },
                            React.createElement('div', {},
                                React.createElement('h1', { className: "text-3xl font-bold text-green-800 mb-2" }, 'SK Blučina'),
                                React.createElement('p', { className: "text-gray-600" }, 'Správa fotbalového týmu')
                            ),
                            React.createElement('div', { className: "text-right" },
                                React.createElement('div', { className: "flex items-center space-x-2 mb-2" },
                                    React.createElement('button', {
                                        onClick: manualSave,
                                        disabled: isSaving,
                                        className: "flex items-center px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
                                    },
                                        React.createElement(Save, { className: "w-4 h-4 mr-1" }),
                                        isSaving ? 'Ukládám...' : 'Uložit'
                                    )
                                ),
                                lastSaved && React.createElement('p', { className: "text-xs text-gray-500" },
                                    `Naposledy uloženo: ${lastSaved.toLocaleString('cs-CZ')}`
                                ),
                                saveError && React.createElement('p', { className: "text-xs text-red-500" },
                                    `Chyba: ${saveError}`
                                )
                            )
                        )
                    ),
                    React.createElement('p', { className: "text-center text-gray-600 mb-4" }, 
                        'Aplikace je funkční - přidejte hráče a testujte ukládání!'
                    )
                )
            );
        };

        ReactDOM.render(React.createElement(FootballTeamManager), document.getElementById('root'));
    </script>
</body>
</html>