<?php
// Pokud je to API požadavek, zpracovat ho
if (isset($_GET['api']) || strpos($_SERVER['REQUEST_URI'], '/api') !== false) {
    header('Content-Type: application/json');
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type');

    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        http_response_code(200);
        exit();
    }

    $dataFile = 'data/team_data.json';

    if (!file_exists('data')) {
        mkdir('data', 0755, true);
    }

    $defaultData = [
        'players' => [
            [
                'id' => 1,
                'firstName' => 'Jan',
                'lastName' => 'Novák',
                'positions' => ['Brankář'],
                'capacity' => 100,
                'status' => 'hraje',
                'number' => 1
            ],
            [
                'id' => 2,
                'firstName' => 'Petr',
                'lastName' => 'Svoboda',
                'positions' => ['Obránce'],
                'capacity' => 85,
                'status' => 'hraje',
                'number' => 5
            ],
            [
                'id' => 3,
                'firstName' => 'Tomáš',
                'lastName' => 'Dvořák',
                'positions' => ['Záložník', 'Útočník'],
                'capacity' => 75,
                'status' => 'zjišťujeme',
                'number' => 10
            ]
        ],
        'staff' => [
            [
                'id' => 1,
                'firstName' => 'Milan',
                'lastName' => 'Kouč',
                'role' => 'Hlavní trenér'
            ],
            [
                'id' => 2,
                'firstName' => 'Pavel',
                'lastName' => 'Asistent',
                'role' => 'Asistent trenéra'
            ]
        ],
        'injured' => [
            [
                'id' => 1,
                'firstName' => 'Martin',
                'lastName' => 'Zraněný',
                'positions' => ['Obránce'],
                'expectedReturn' => '2025-07-01'
            ]
        ],
        'formation' => [],
        'lastUpdated' => date('Y-m-d H:i:s')
    ];

    function loadData($file, $default) {
        if (file_exists($file)) {
            $content = file_get_contents($file);
            if ($content !== false) {
                $data = json_decode($content, true);
                if ($data !== null) {
                    return $data;
                }
            }
        }
        return $default;
    }

    function saveData($file, $data) {
        $data['lastUpdated'] = date('Y-m-d H:i:s');
        $jsonData = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        return file_put_contents($file, $jsonData, LOCK_EX) !== false;
    }

    $method = $_SERVER['REQUEST_METHOD'];
    $input = json_decode(file_get_contents('php://input'), true);

    try {
        switch ($method) {
            case 'GET':
                $data = loadData($dataFile, $defaultData);
                echo json_encode(['success' => true, 'data' => $data]);
                break;

            case 'POST':
                if (!$input) {
                    throw new Exception('Neplatná data');
                }
                
                $currentData = loadData($dataFile, $defaultData);
                
                if (isset($input['players'])) {
                    $currentData['players'] = $input['players'];
                }
                if (isset($input['staff'])) {
                    $currentData['staff'] = $input['staff'];
                }
                if (isset($input['injured'])) {
                    $currentData['injured'] = $input['injured'];
                }
                if (isset($input['formation'])) {
                    $currentData['formation'] = $input['formation'];
                }
                
                if (saveData($dataFile, $currentData)) {
                    echo json_encode(['success' => true, 'message' => 'Data uložena']);
                } else {
                    throw new Exception('Chyba při ukládání dat');
                }
                break;

            case 'PUT':
                if (!$input || !isset($input['section']) || !isset($input['data'])) {
                    throw new Exception('Neplatný formát dat');
                }
                
                $currentData = loadData($dataFile, $defaultData);
                $section = $input['section'];
                
                if (!in_array($section, ['players', 'staff', 'injured', 'formation'])) {
                    throw new Exception('Neplatná sekce');
                }
                
                $currentData[$section] = $input['data'];
                
                if (saveData($dataFile, $currentData)) {
                    echo json_encode(['success' => true, 'message' => "Sekce $section aktualizována"]);
                } else {
                    throw new Exception('Chyba při ukládání dat');
                }
                break;

            default:
                throw new Exception('Neplatná HTTP metoda');
        }

    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
    exit();
}
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SK Blučina - Správa týmu</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect, useRef } = React;
        const { Plus, Edit3, Trash2, Users, Heart, Shield, Save, RefreshCw } = lucide;

        // API funkce - používá stejný soubor s ?api parametrem
        const API = {
            baseUrl: window.location.pathname + '?api=1',

            async loadData() {
                try {
                    const response = await fetch(this.baseUrl);
                    const result = await response.json();
                    if (result.success) {
                        return result.data;
                    } else {
                        throw new Error(result.error || 'Chyba při načítání dat');
                    }
                } catch (error) {
                    console.error('Chyba při načítání dat:', error);
                    throw error;
                }
            },

            async saveData(data) {
                try {
                    const response = await fetch(this.baseUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(data)
                    });
                    const result = await response.json();
                    if (!result.success) {
                        throw new Error(result.error || 'Chyba při ukládání dat');
                    }
                    return result;
                } catch (error) {
                    console.error('Chyba při ukládání dat:', error);
                    throw error;
                }
            },

            async saveSection(section, data) {
                try {
                    const response = await fetch(this.baseUrl, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ section, data })
                    });
                    const result = await response.json();
                    if (!result.success) {
                        throw new Error(result.error || 'Chyba při ukládání sekce');
                    }
                    return result;
                } catch (error) {
                    console.error('Chyba při ukládání sekce:', error);
                    throw error;
                }
            }
        };

        const FootballTeamManager = () => {
            // Výchozí data
            const defaultData = {
                players: [
                    { id: 1, firstName: 'Jan', lastName: 'Novák', positions: ['Brankář'], capacity: 100, status: 'hraje', number: 1 },
                    { id: 2, firstName: 'Petr', lastName: 'Svoboda', positions: ['Obránce'], capacity: 85, status: 'hraje', number: 5 },
                    { id: 3, firstName: 'Tomáš', lastName: 'Dvořák', positions: ['Záložník', 'Útočník'], capacity: 75, status: 'zjišťujeme', number: 10 }
                ],
                staff: [
                    { id: 1, firstName: 'Milan', lastName: 'Kouč', role: 'Hlavní trenér' },
                    { id: 2, firstName: 'Pavel', lastName: 'Asistent', role: 'Asistent trenéra' }
                ],
                injured: [
                    { id: 1, firstName: 'Martin', lastName: 'Zraněný', positions: ['Obránce'], expectedReturn: '2025-07-01' }
                ],
                formation: []
            };

            // Stavy
            const [players, setPlayers] = useState(defaultData.players);
            const [staff, setStaff] = useState(defaultData.staff);
            const [injured, setInjured] = useState(defaultData.injured);
            const [formation, setFormation] = useState(defaultData.formation);
            const [activeTab, setActiveTab] = useState('players');
            const [showForm, setShowForm] = useState(false);
            const [editingPlayer, setEditingPlayer] = useState(null);
            const [draggedPlayer, setDraggedPlayer] = useState(null);
            const [isLoading, setIsLoading] = useState(true);
            const [isSaving, setIsSaving] = useState(false);
            const [lastSaved, setLastSaved] = useState(null);
            const [saveError, setSaveError] = useState(null);

            // Formulářové stavy
            const [formData, setFormData] = useState({
                firstName: '', lastName: '', positions: [], capacity: 100, status: 'hraje', number: ''
            });

            // Načtení dat při startu
            useEffect(() => {
                loadInitialData();
            }, []);

            const loadInitialData = async () => {
                try {
                    setIsLoading(true);
                    const data = await API.loadData();
                    setPlayers(data.players || defaultData.players);
                    setStaff(data.staff || defaultData.staff);
                    setInjured(data.injured || defaultData.injured);
                    setFormation(data.formation || defaultData.formation);
                    setLastSaved(data.lastUpdated ? new Date(data.lastUpdated) : null);
                } catch (error) {
                    console.error('Chyba při načítání:', error);
                    // Použít výchozí data při chybě
                    setPlayers(defaultData.players);
                    setStaff(defaultData.staff);
                    setInjured(defaultData.injured);
                    setFormation(defaultData.formation);
                } finally {
                    setIsLoading(false);
                }
            };

            // Automatické ukládání při změnách
            const saveToServer = async (section, data) => {
                try {
                    setIsSaving(true);
                    setSaveError(null);
                    await API.saveSection(section, data);
                    setLastSaved(new Date());
                } catch (error) {
                    setSaveError(error.message);
                    console.error('Chyba při ukládání:', error);
                } finally {
                    setIsSaving(false);
                }
            };

            // Debounced save function
            const debouncedSave = useRef();
            useEffect(() => {
                if (debouncedSave.current) {
                    clearTimeout(debouncedSave.current);
                }
                debouncedSave.current = setTimeout(() => {
                    if (!isLoading) {
                        saveToServer('players', players);
                    }
                }, 1000);
            }, [players, isLoading]);

            useEffect(() => {
                if (debouncedSave.current) {
                    clearTimeout(debouncedSave.current);
                }
                debouncedSave.current = setTimeout(() => {
                    if (!isLoading) {
                        saveToServer('formation', formation);
                    }
                }, 1000);
            }, [formation, isLoading]);

            // Pozice na hřišti
            const fieldPositions = [
                { id: 'gk', x: 50, y: 85, label: 'GK' },
                { id: 'lb', x: 15, y: 65, label: 'LB' },
                { id: 'cb1', x: 35, y: 65, label: 'CB' },
                { id: 'cb2', x: 65, y: 65, label: 'CB' },
                { id: 'rb', x: 85, y: 65, label: 'RB' },
                { id: 'lm', x: 15, y: 35, label: 'LM' },
                { id: 'cm1', x: 35, y: 35, label: 'CM' },
                { id: 'cm2', x: 65, y: 35, label: 'CM' },
                { id: 'rm', x: 85, y: 35, label: 'RM' },
                { id: 'st1', x: 35, y: 15, label: 'ST' },
                { id: 'st2', x: 65, y: 15, label: 'ST' }
            ];

            const handleSubmit = () => {
                if (!formData.firstName.trim() || !formData.lastName.trim()) {
                    alert('Vyplňte prosím jméno a příjmení');
                    return;
                }
                
                if (editingPlayer) {
                    setPlayers(players.map(p => p.id === editingPlayer.id ? 
                        { ...editingPlayer, ...formData, id: editingPlayer.id } : p
                    ));
                    setEditingPlayer(null);
                } else {
                    const newPlayer = {
                        id: Date.now(),
                        ...formData,
                        positions: formData.positions.filter(p => p.trim())
                    };
                    setPlayers([...players, newPlayer]);
                }
                setFormData({ firstName: '', lastName: '', positions: [], capacity: 100, status: 'hraje', number: '' });
                setShowForm(false);
            };

            const handleEdit = (player) => {
                setEditingPlayer(player);
                setFormData({
                    firstName: player.firstName,
                    lastName: player.lastName,
                    positions: player.positions,
                    capacity: player.capacity,
                    status: player.status,
                    number: player.number || ''
                });
                setShowForm(true);
            };

            const handleDelete = (id) => {
                if (confirm('Opravdu chcete odstranit tohoto hráče?')) {
                    setPlayers(players.filter(p => p.id !== id));
                }
            };

            const handleDragStart = (e, player) => {
                setDraggedPlayer(player);
                e.dataTransfer.effectAllowed = 'move';
            };

            const handleDragOver = (e) => {
                e.preventDefault();
            };

            const handleDrop = (e, position) => {
                e.preventDefault();
                if (draggedPlayer) {
                    const newFormation = formation.filter(p => p.position !== position.id);
                    newFormation.push({ ...draggedPlayer, position: position.id });
                    setFormation(newFormation);
                    setDraggedPlayer(null);
                }
            };

            const getPlayerAtPosition = (positionId) => {
                return formation.find(p => p.position === positionId);
            };

            const getStatusColor = (status) => {
                switch (status) {
                    case 'hraje': return 'bg-green-100 text-green-800';
                    case 'zjišťujeme': return 'bg-yellow-100 text-yellow-800';
                    case 'odmítl': return 'bg-red-100 text-red-800';
                    default: return 'bg-gray-100 text-gray-800';
                }
            };

            const getCapacityColor = (capacity) => {
                if (capacity >= 90) return 'text-green-600';
                if (capacity >= 70) return 'text-yellow-600';
                return 'text-red-600';
            };

            const manualSave = async () => {
                try {
                    setIsSaving(true);
                    setSaveError(null);
                    await API.saveData({ players, staff, injured, formation });
                    setLastSaved(new Date());
                    alert('Data úspěšně uložena!');
                } catch (error) {
                    setSaveError(error.message);
                    alert('Chyba při ukládání: ' + error.message);
                } finally {
                    setIsSaving(false);
                }
            };

            if (isLoading) {
                return React.createElement('div', { className: "min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center" },
                    React.createElement('div', { className: "text-center" },
                        React.createElement('div', { className: "animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4" }),
                        React.createElement('p', { className: "text-gray-600" }, 'Načítám data...')
                    )
                );
            }

            return React.createElement('div', { className: "min-h-screen bg-gradient-to-br from-green-50 to-blue-50 p-4" },
                React.createElement('div', { className: "max-w-7xl mx-auto" },
                    // Hlavička
                    React.createElement('div', { className: "bg-white rounded-lg shadow-lg p-6 mb-6" },
                        React.createElement('div', { className: "flex justify-between items-center" },
                            React.createElement('div', {},
                                React.createElement('h1', { className: "text-3xl font-bold text-green-800 mb-2" }, 'SK Blučina'),
                                React.createElement('p', { className: "text-gray-600" }, 'Správa fotbalového týmu')
                            ),
                            React.createElement('div', { className: "text-right" },
                                React.createElement('div', { className: "flex items-center space-x-2 mb-2" },
                                    React.createElement('button', {
                                        onClick: manualSave,
                                        disabled: isSaving,
                                        className: "flex items-center px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
                                    },
                                        React.createElement(Save, { className: "w-4 h-4 mr-1" }),
                                        isSaving ? 'Ukládám...' : 'Uložit'
                                    )
                                ),
                                lastSaved && React.createElement('p', { className: "text-xs text-gray-500" },
                                    `Naposledy uloženo: ${lastSaved.toLocaleString('cs-CZ')}`
                                ),
                                saveError && React.createElement('p', { className: "text-xs text-red-500" },
                                    `Chyba: ${saveError}`
                                )
                            )
                        )
                    ),

                    // Navigační záložky
                    React.createElement('div', { className: "bg-white rounded-lg shadow-lg mb-6" },
                        React.createElement('div', { className: "flex border-b" },
                            ['players', 'formation', 'staff', 'injured'].map(tab =>
                                React.createElement('button', {
                                    key: tab,
                                    onClick: () => setActiveTab(tab),
                                    className: `px-6 py-3 font-medium ${activeTab === tab
                                        ? 'text-green-600 border-b-2 border-green-600'
                                        : 'text-gray-500 hover:text-gray-700'}`
                                },
                                    tab === 'players' ? 'Hráči' :
                                    tab === 'formation' ? 'Sestava' :
                                    tab === 'staff' ? 'Realizační tým' : 'Zranění'
                                )
                            )
                        ),

                        // Obsah záložek
                        React.createElement('div', { className: "p-6" },
                            // Záložka Hráči
                            activeTab === 'players' && React.createElement('div', {},
                                React.createElement('div', { className: "flex justify-between items-center mb-6" },
                                    React.createElement('h2', { className: "text-xl font-semibold flex items-center" },
                                        React.createElement(Users, { className: "w-5 h-5 mr-2" }),
                                        'Hráči'
                                    ),
                                    React.createElement('button', {
                                        onClick: () => setShowForm(true),
                                        className: "bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center"
                                    },
                                        React.createElement(Plus, { className: "w-4 h-4 mr-2" }),
                                        'Přidat hráče'
                                    )
                                ),
                                React.createElement('div', { className: "grid gap-4" },
                                    players.map(player =>
                                        React.createElement('div', {
                                            key: player.id,
                                            draggable: true,
                                            onDragStart: (e) => handleDragStart(e, player),
                                            className: "bg-gray-50 p-4 rounded-lg border cursor-move hover:shadow-md transition-shadow"
                                        },
                                            React.createElement('div', { className: "flex justify-between items-start" },
                                                React.createElement('div', { className: "flex-1" },
                                                    React.createElement('div', { className: "flex items-center mb-2" },
                                                        React.createElement('h3', { className: "font-semibold text-lg" },
                                                            `${player.firstName} ${player.lastName}`
                                                        ),
                                                        player.number && React.createElement('span', {
                                                            className: "ml-2 bg-green-600 text-white px-2 py-1 rounded text-sm"
                                                        }, `#${player.number}`)
                                                    ),
                                                    React.createElement('p', { className: "text-gray-600 mb-2" },
                                                        `Pozice: ${player.positions.join(', ')}`
                                                    ),
                                                    React.createElement('div', { className: "flex items-center space-x-4" },
                                                        React.createElement('span', {
                                                            className: `px-2 py-1 rounded text-sm ${getStatusColor(player.status)}`
                                                        }, player.status),
                                                        React.createElement('span', {
                                                            className: `font-semibold ${getCapacityColor(player.capacity)}`
                                                        }, `${player.capacity}%`)
                                                    )
                                                ),
                                                React.createElement('div', { className: "flex space-x-2" },
                                                    React.createElement('button', {
                                                        onClick: () => handleEdit(player),
                                                        className: "p-2 text-blue-600 hover:bg-blue-100 rounded"
                                                    },
                                                        React.createElement(Edit3, { className: "w-4 h-4" })
                                                    ),
                                                    React.createElement('button', {
                                                        onClick: () => handleDelete(player.id),
                                                        className: "p-2 text-red-600 hover:bg-red-100 rounded"
                                                    },
                                                        React.createElement(Trash2, { className: "w-4 h-4" })
                                                    )
                                                )
                                            )
                                        )
                                    )
                                )
                            ),

                            // Záložka Sestava
                            activeTab === 'formation' && React.createElement('div', {},
                                React.createElement('h2', { className: "text-xl font-semibold mb-6" }, 'Sestava týmu'),
                                React.createElement('div', { className: "bg-green-100 rounded-lg p-8 relative" },
                                    React.createElement('div', {
                                        className: "relative w-full h-96 bg-green-200 rounded border-2 border-white",
                                        style: { backgroundImage: 'linear-gradient(90deg, rgba(255,255,255,0.2) 50%, transparent 50%)' }
                                    },
                                        fieldPositions.map(position => {
                                            const playerAtPosition = getPlayerAtPosition(position.id);
                                            return React.createElement('div', {
                                                key: position.id,
                                                className: "absolute w-16 h-16 rounded-full border-2 border-white flex items-center justify-center text-xs font-bold cursor-pointer",
                                                style: {
                                                    left: `${position.x}%`,
                                                    top: `${position.y}%`,
                                                    transform: 'translate(-50%, -50%)',
                                                    backgroundColor: playerAtPosition ? '#10b981' : '#6b7280',
                                                    color: 'white'
                                                },
                                                onDragOver: handleDragOver,
                                                onDrop: (e) => handleDrop(e, position)
                                            },
                                                playerAtPosition ?
                                                    React.createElement('div', { className: "text-center" },
                                                        React.createElement('div', { className: "text-xs" }, playerAtPosition.firstName),
                                                        React.createElement('div', { className: "text-xs" }, playerAtPosition.lastName)
                                                    ) :
                                                    position.label
                                            );
                                        })
                                    )
                                ),
                                React.createElement('p', { className: "text-gray-600 mt-4 text-center" },
                                    'Přetáhněte hráče z karty "Hráči" na pozice v sestavě'
                                )
                            ),

                            // Záložka Realizační tým
                            activeTab === 'staff' && React.createElement('div', {},
                                React.createElement('h2', { className: "text-xl font-semibold mb-6 flex items-center" },
                                    React.createElement(Shield, { className: "w-5 h-5 mr-2" }),
                                    'Realizační tým'
                                ),
                                React.createElement('div', { className: "grid gap-4" },
                                    staff.map(member =>
                                        React.createElement('div', {
                                            key: member.id,
                                            className: "bg-blue-50 p-4 rounded-lg border"
                                        },
                                            React.createElement('h3', { className: "font-semibold text-lg" },
                                                `${member.firstName} ${member.lastName}`
                                            ),
                                            React.createElement('p', { className: "text-gray-600" }, member.role)
                                        )
                                    )
                                )
                            ),

                            // Záložka Zranění
                            activeTab === 'injured' && React.createElement('div', {},
                                React.createElement('h2', { className: "text-xl font-semibold mb-6 flex items-center" },
                                    React.createElement(Heart, { className: "w-5 h-5 mr-2" }),
                                    'Zranění hráči'
                                ),
                                React.createElement('div', { className: "grid gap-4" },
                                    injured.map(player =>
                                        React.createElement('div', {
                                            key: player.id,
                                            className: "bg-red-50 p-4 rounded-lg border"
                                        },
                                            React.createElement('h3', { className: "font-semibold text-lg" },
                                                `${player.firstName} ${player.lastName}`
                                            ),
                                            React.createElement('p', { className: "text-gray-600 mb-2" },
                                                `Pozice: ${player.positions.join(', ')}`
                                            ),
                                            React.createElement('p', { className: "text-red-600" },
                                                `Očekávaný návrat: ${new Date(player.expectedReturn).toLocaleDateString('cs-CZ')}`
                                            )
                                        )
                                    )
                                )
                            )
                        )
                    ),

                    // Formulář pro přidání/úpravu hráče
                    showForm && React.createElement('div', {
                        className: "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
                    },
                        React.createElement('div', { className: "bg-white rounded-lg p-6 w-full max-w-md" },
                            React.createElement('h3', { className: "text-lg font-semibold mb-4" },
                                editingPlayer ? 'Upravit hráče' : 'Přidat nového hráče'
                            ),
                            React.createElement('div', { className: "space-y-4" },
                                React.createElement('div', {},
                                    React.createElement('label', { className: "block text-sm font-medium text-gray-700 mb-1" }, 'Jméno'),
                                    React.createElement('input', {
                                        type: 'text',
                                        value: formData.firstName,
                                        onChange: (e) => setFormData({...formData, firstName: e.target.value}),
                                        className: "w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                    })
                                ),
                                React.createElement('div', {},
                                    React.createElement('label', { className: "block text-sm font-medium text-gray-700 mb-1" }, 'Příjmení'),
                                    React.createElement('input', {
                                        type: 'text',
                                        value: formData.lastName,
                                        onChange: (e) => setFormData({...formData, lastName: e.target.value}),
                                        className: "w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                    })
                                ),
                                React.createElement('div', {},
                                    React.createElement('label', { className: "block text-sm font-medium text-gray-700 mb-1" }, 'Číslo dresu'),
                                    React.createElement('input', {
                                        type: 'number',
                                        value: formData.number,
                                        onChange: (e) => setFormData({...formData, number: e.target.value}),
                                        className: "w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                    })
                                ),
                                React.createElement('div', {},
                                    React.createElement('label', { className: "block text-sm font-medium text-gray-700 mb-1" }, 'Pozice'),
                                    React.createElement('select', {
                                        multiple: true,
                                        value: formData.positions,
                                        onChange: (e) => setFormData({...formData, positions: Array.from(e.target.selectedOptions, option => option.value)}),
                                        className: "w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                    },
                                        ['Brankář', 'Obránce', 'Záložník', 'Útočník'].map(pos =>
                                            React.createElement('option', { key: pos, value: pos }, pos)
                                        )
                                    )
                                ),
                                React.createElement('div', {},
                                    React.createElement('label', { className: "block text-sm font-medium text-gray-700 mb-1" }, 'Kondice (%)'),
                                    React.createElement('input', {
                                        type: 'range',
                                        min: '0',
                                        max: '100',
                                        value: formData.capacity,
                                        onChange: (e) => setFormData({...formData, capacity: parseInt(e.target.value)}),
                                        className: "w-full"
                                    }),
                                    React.createElement('div', { className: "text-center text-sm text-gray-600" }, `${formData.capacity}%`)
                                ),
                                React.createElement('div', {},
                                    React.createElement('label', { className: "block text-sm font-medium text-gray-700 mb-1" }, 'Stav'),
                                    React.createElement('select', {
                                        value: formData.status,
                                        onChange: (e) => setFormData({...formData, status: e.target.value}),
                                        className: "w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                    },
                                        React.createElement('option', { value: 'hraje' }, 'Hraje'),
                                        React.createElement('option', { value: 'zjišťujeme' }, 'Zjišťujeme'),
                                        React.createElement('option', { value: 'odmítl' }, 'Odmítl')
                                    )
                                )
                            ),
                            React.createElement('div', { className: "flex justify-end space-x-3 mt-6" },
                                React.createElement('button', {
                                    onClick: () => {
                                        setShowForm(false);
                                        setEditingPlayer(null);
                                        setFormData({ firstName: '', lastName: '', positions: [], capacity: 100, status: 'hraje', number: '' });
                                    },
                                    className: "px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
                                }, 'Zrušit'),
                                React.createElement('button', {
                                    onClick: handleSubmit,
                                    className: "px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                                }, editingPlayer ? 'Uložit změny' : 'Přidat hráče')
                            )
                        )
                    )
                )
            );
        };

        ReactDOM.render(React.createElement(FootballTeamManager), document.getElementById('root'));
    </script>
</body>
</html>