<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - SK Bluč<PERSON></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #166534;
            margin: 0;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .success {
            background-color: #dcfce7;
            border-color: #16a34a;
            color: #166534;
        }
        .error {
            background-color: #fef2f2;
            border-color: #dc2626;
            color: #991b1b;
        }
        .loading {
            background-color: #fef3c7;
            border-color: #d97706;
            color: #92400e;
        }
        button {
            background-color: #16a34a;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #15803d;
        }
        .player-list {
            margin-top: 20px;
        }
        .player-item {
            background-color: #f9fafb;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border: 1px solid #e5e7eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>SK Blučina - Test aplikace</h1>
            <p>Testování základní funkcionality</p>
        </div>

        <div id="status" class="status loading">
            Načítám aplikaci...
        </div>

        <div id="content" style="display: none;">
            <h2>Hráči týmu</h2>
            <button onclick="loadPlayers()">Načíst hráče</button>
            <button onclick="addTestPlayer()">Přidat testovacího hráče</button>
            
            <div id="players" class="player-list">
                <!-- Zde se zobrazí hráči -->
            </div>
        </div>
    </div>

    <script>
        // Globální proměnné
        let players = [];
        
        // API funkce
        const API = {
            baseUrl: window.location.pathname.replace('test.html', 'index.php') + '?api=1',
            
            async loadData() {
                try {
                    const response = await fetch(this.baseUrl);
                    const result = await response.json();
                    if (result.success) {
                        return result.data;
                    } else {
                        throw new Error(result.error || 'Chyba při načítání dat');
                    }
                } catch (error) {
                    console.error('Chyba při načítání dat:', error);
                    throw error;
                }
            },
            
            async saveData(data) {
                try {
                    const response = await fetch(this.baseUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(data)
                    });
                    const result = await response.json();
                    if (!result.success) {
                        throw new Error(result.error || 'Chyba při ukládání dat');
                    }
                    return result;
                } catch (error) {
                    console.error('Chyba při ukládání dat:', error);
                    throw error;
                }
            }
        };

        // Funkce pro zobrazení statusu
        function showStatus(message, type = 'loading') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }

        // Funkce pro zobrazení hráčů
        function displayPlayers() {
            const playersEl = document.getElementById('players');
            if (players.length === 0) {
                playersEl.innerHTML = '<p>Žádní hráči nenalezeni.</p>';
                return;
            }
            
            playersEl.innerHTML = players.map(player => `
                <div class="player-item">
                    <strong>${player.firstName} ${player.lastName}</strong>
                    ${player.number ? `(#${player.number})` : ''}
                    <br>
                    Pozice: ${player.positions.join(', ')}
                    <br>
                    Kondice: ${player.capacity}% | Stav: ${player.status}
                </div>
            `).join('');
        }

        // Načtení hráčů
        async function loadPlayers() {
            try {
                showStatus('Načítám hráče...', 'loading');
                const data = await API.loadData();
                players = data.players || [];
                displayPlayers();
                showStatus(`Úspěšně načteno ${players.length} hráčů`, 'success');
            } catch (error) {
                showStatus(`Chyba při načítání: ${error.message}`, 'error');
            }
        }

        // Přidání testovacího hráče
        async function addTestPlayer() {
            try {
                showStatus('Přidávám testovacího hráče...', 'loading');
                
                const newPlayer = {
                    id: Date.now(),
                    firstName: 'Test',
                    lastName: 'Hráč',
                    positions: ['Záložník'],
                    capacity: 90,
                    status: 'hraje',
                    number: Math.floor(Math.random() * 99) + 1
                };
                
                players.push(newPlayer);
                
                await API.saveData({ players });
                displayPlayers();
                showStatus('Testovací hráč úspěšně přidán', 'success');
            } catch (error) {
                showStatus(`Chyba při přidávání: ${error.message}`, 'error');
            }
        }

        // Inicializace aplikace
        async function init() {
            try {
                showStatus('Inicializuji aplikaci...', 'loading');
                
                // Test API připojení
                await API.loadData();
                
                // Zobrazit obsah
                document.getElementById('content').style.display = 'block';
                showStatus('Aplikace je připravena', 'success');
                
                // Automaticky načíst hráče
                await loadPlayers();
                
            } catch (error) {
                showStatus(`Chyba při inicializaci: ${error.message}`, 'error');
                console.error('Inicializační chyba:', error);
            }
        }

        // Spustit po načtení stránky
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
